<!DOCTYPE html>
<html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <meta charset="utf-8"/>

        <title>黑马GenkiPi模拟器</title>

        <style>
            html, body {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
                overflow: hidden; /* Prevents the whole page from scrolling */
                font-family: Arial, sans-serif;
                height: 100%; /* Ensure full height */
                width: 100%;  /* Ensure full width */
            }

            .controls {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                display: flex;
                gap: 10px;
                align-items: center;
            }

            #modelSelect {
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #ccc;
                background: white;
            }

            .github-link {
                color: white;
                text-decoration: none;
                padding: 8px;
                background: #333;
                border-radius: 4px;
                display: inline-block;
            }

            .github-link:hover {
                background: #444;
            }

            .control-panel {
                position: fixed;
                bottom: 20px;
                left: 20px;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 15px;
                border-radius: 8px;
                z-index: 1000;
                max-width: 350px;
                /* min-height: 300px; /* Consider removing or adjusting if causing issues with small viewports */
                max-height: calc(100vh - 40px); /* Limit height to viewport minus spacing */
                overflow-y: auto; /* Allow vertical scroll ONLY when needed */
                overflow-x: hidden; /* Prevent horizontal scroll */

                /* --- Scrollbar Hiding --- */
                /* For Webkit browsers (Chrome, Safari, Edge) */
                &::-webkit-scrollbar {
                    display: none; /* Hide scrollbar */
                    width: 0;  /* Remove scrollbar space */
                    height: 0; /* Remove scrollbar space */
                }
                /* For Firefox */
                scrollbar-width: none;
                /* For IE/Edge (Older versions) */
                -ms-overflow-style: none;
                /* --- End Scrollbar Hiding --- */
            }

            .control-panel h3 {
                margin-top: 0;
                margin-bottom: 10px;
            }

            .control-info {
                margin-bottom: 15px;
            }

            .control-pair {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
                align-items: center;
            }

            .control-pair > div {
                flex: 1;
                text-align: center;
            }

            .control-pair > div:first-child {
                text-align: left;
            }

            .control-pair > div:last-child {
                text-align: right;
            }

            .joint-control {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
                align-items: center;
            }

            .joint-name {
                flex: 0 0 100px;
                text-align: left;
                padding-right: 15px;
                font-weight: 500;
            }

            .joint-keys {
                flex: 1;
                white-space: nowrap;
            }

            .joint-keys .key:nth-child(3) {
                margin-left: 10px;
            }

            .key {
                background: rgba(255, 255, 255, 0.2);
                padding: 2px 6px;
                border-radius: 4px;
                font-family: monospace;
            }

            .key-pressed {
                background: rgba(255, 255, 255, 0.6);
                color: #000;
                box-shadow: 0 0 8px rgba(76, 175, 80, 0.8);
            }

            .direction-plus {
                color: #4CAF50;
                font-weight: bold;
                margin-left: 4px;
            }

            .direction-minus {
                color: #F44336;
                font-weight: bold;
                margin-left: 4px;
            }

            .control-status {
                display: flex;
                align-items: center;
                margin-top: 10px;
            }

            .status-indicator {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                margin-right: 8px;
            }

            .active {
                background: #4CAF50;
            }

            .inactive {
                background: #F44336;
            }

            .toggle-panel {
                position: fixed;
                bottom: 20px;
                left: 20px;
                background: #333;
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                z-index: 1001;
                display: none;
                font-weight: bold;
            }

            .toggle-panel:hover {
                background: #444;
            }

            .speed-control {
                margin: 15px 0;
                padding: 10px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 6px;
            }

            .speed-control label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }

            .speed-slider {
                width: 100%;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .speed-slider input {
                flex: 1;
            }

            .speed-value {
                min-width: 30px;
                text-align: center;
                background: rgba(255, 255, 255, 0.2);
                padding: 3px 6px;
                border-radius: 4px;
            }

            /* Real Robot Control Styles */
            .real-robot-control {
                margin-top: 15px;
                padding: 10px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 6px;
            }

            .real-robot-control h4 {
                margin-top: 0;
                margin-bottom: 10px;
            }

            .real-robot-control button {
                display: block;
                width: 100%;
                padding: 8px;
                margin-top: 10px;
                background: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-weight: bold;
            }

            .real-robot-control button:hover {
                background: #0b7dda;
            }

            .real-robot-control button:disabled {
                background: #cccccc;
                cursor: not-allowed;
            }

            /* Connect Button Style */
            .connect-button {
                display: block;
                width: 100%;
                padding: 8px;
                margin: 10px 0 15px 0;
                background: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-weight: bold;
                transition: background-color 0.3s;
            }

            .connect-button:hover {
                background: #0b7dda;
            }

            .connect-button:disabled {
                background: #cccccc;
                cursor: not-allowed;
            }

            .connect-button.connected {
                background: #4CAF50;
            }

            .connect-button.connected:hover {
                background: #3d8b40;
            }

            /* Collapsible section styles */
            .collapsible {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 6px;
                margin-bottom: 10px;
                overflow: hidden;
                width: 100%; /* Use full width of parent */
            }

            .collapsible-header {
                padding: 10px;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: bold;
            }

            .collapsible-header:hover {
                background: rgba(255, 255, 255, 0.15);
            }

            .collapsible-content {
                padding: 0 10px 10px 10px;
                display: none;
            }

            .collapsible.open .collapsible-content {
                display: block;
            }

            .collapsible-icon {
                transition: transform 0.3s;
            }

            .collapsible.open .collapsible-icon {
                transform: rotate(180deg);
            }

            /* Active control section */
            .control-active {
                box-shadow: 0 0 8px rgba(76, 175, 80, 0.8);
            }

            /* Global Controls Section */
            .global-controls {
                margin-bottom: 15px;
            }

            /* Panel header with title and close button */
            .panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }

            .panel-header h3 {
                margin: 0;
            }

            .panel-header span {
                font-size: 20px;
                font-weight: bold;
            }

            /* Help tooltip styles */
            .help-wrapper {
                position: relative;
                display: inline-flex;
                align-items: center;
                width: 100%;
            }

            .help-icon {
                display: inline-flex;
                justify-content: center;
                align-items: center;
                width: 18px;
                height: 18px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.3);
                color: #fff;
                font-size: 12px;
                font-weight: bold;
                margin-left: 10px;
                cursor: help;
            }

            .tooltip {
                visibility: hidden;
                position: absolute;
                top: -90px; /* Adjust as needed */
                right: 0;
                background: rgba(0, 0, 0, 0.9);
                border: 1px solid rgba(255, 255, 255, 0.2);
                color: white;
                border-radius: 4px;
                padding: 10px;
                width: 250px;
                font-size: 12px;
                z-index: 1002;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                opacity: 0;
                transition: opacity 0.3s;
            }

            .tooltip ul {
                margin: 5px 0;
                padding-left: 20px;
            }

            .tooltip li {
                margin-bottom: 5px;
            }

            .help-icon:hover .tooltip,
            .help-icon.active .tooltip {
                visibility: visible;
                opacity: 1;
            }

            .servo-status {
                font-weight: bold;
                min-width: 60px;
            }

            .servo-status.warning {
                color: #FF9800;
            }

            .servo-error {
                font-size: 12px;
                color: #F44336;
                display: none;
                margin-left: 10px;
                max-width: 200px;
                word-wrap: break-word;
            }

            /* 舵机和关节限位提醒 */
            #servoLimitAlert {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(244, 67, 54, 0.9);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 2000;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                display: none;
                text-align: center;
                max-width: 400px;
                font-weight: bold;
                animation: fadeInOut 0.3s ease;
                pointer-events: none; /* 确保不阻碍用户点击 */
            }

            #jointLimitAlert {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(255, 152, 0, 0.9);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 2000;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                display: none;
                text-align: center;
                max-width: 400px;
                font-weight: bold;
                animation: fadeInOut 0.3s ease;
                pointer-events: none; /* 确保不阻碍用户点击 */
            }

            @keyframes fadeInOut {
                0% { opacity: 0; transform: translate(-50%, -20px); }
                100% { opacity: 1; transform: translate(-50%, 0); }
            }
            /* Servo Status Grid styles moved inside general styles */
            .servo-status-grid {
                display: grid;
                grid-template-columns: auto 1fr auto;
                gap: 5px;
                margin-top: 10px;
            }
            .servo-status-row {
                display: contents;
            }
            .servo-name {
                font-weight: bold;
            }
            .servo-status {
                text-align: center;
            }
            .servo-error {
                grid-column: span 3;
                font-size: 12px;
                color: #F44336;
                display: none;
                margin-bottom: 5px;
                background: rgba(244, 67, 54, 0.1);
                padding: 3px;
                border-radius: 3px;
            }
        </style>
    </head>
    <body>


        <!-- 添加舵机限位和错误提醒框 -->
        <div id="servoLimitAlert"></div>
        <div id="jointLimitAlert"></div>

        <div class="control-panel" id="controlPanel">
            <div class="panel-header">
                <h3>黑马GenkiPi模拟器 </h3>
                <span id="hideControls" style="cursor: pointer; padding: 0 5px;">×</span>
            </div>

            <div class="global-controls">
                <!-- Speed control (global setting) -->
                <div class="speed-control">
                    <label for="speedControl">速度控制:</label>
                    <div class="speed-slider">
                        <input type="range" id="speedControl" min="0.1" max="1" step="0.1" value="0.8">
                        <span class="speed-value" id="speedValue">0.8</span>
                    </div>
                </div>

                <!-- Connect Real Robot button (global) -->
            </div>

            <!-- Control methods section with collapsible panels -->
            <div class="control-methods">
                <!-- Keyboard Controls -->
                <div class="collapsible open" id="keyboardControlSection">
                    <div class="collapsible-header">
                        <span>键盘控制</span>
                        <span class="collapsible-icon">▼</span>
                    </div>
                    <div class="collapsible-content">
                        <!-- 坐标控制 (主要控制方式) -->
                        <div style="margin-bottom: 15px; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 6px;">
                            <h4 style="margin: 0 0 10px 0; color: #4CAF50;">坐标控制 (6轴联动)</h4>
                            <div class="joint-control">
                                <div class="joint-name">X轴控制:</div>
                                <div class="joint-keys"><span class="key" data-key="u">U</span> <span class="direction-plus">+</span> <span class="key" data-key="i">I</span> <span class="direction-minus">-</span></div>
                            </div>
                            <div class="joint-control">
                                <div class="joint-name">Y轴控制:</div>
                                <div class="joint-keys"><span class="key" data-key="j">J</span> <span class="direction-plus">+</span> <span class="key" data-key="k">K</span> <span class="direction-minus">-</span></div>
                            </div>
                            <div class="joint-control">
                                <div class="joint-name">Z轴控制:</div>
                                <div class="joint-keys"><span class="key" data-key="n">N</span> <span class="direction-plus">+</span> <span class="key" data-key="m">M</span> <span class="direction-minus">-</span></div>
                            </div>
                        </div>

                        <!-- 末端执行器位置显示 -->
                        <div style="margin-bottom: 15px; padding: 10px; background: rgba(33, 150, 243, 0.1); border-radius: 6px;">
                            <h4 style="margin: 0 0 10px 0; color: #2196F3;">末端执行器位置</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; font-family: monospace;">
                                <div>X: <span id="endEffectorX">0.000</span>m</div>
                                <div>Y: <span id="endEffectorY">0.000</span>m</div>
                                <div>Z: <span id="endEffectorZ">0.000</span>m</div>
                            </div>
                        </div>

                        <!-- 传统关节控制 (备用) -->
                        <div style="margin-bottom: 10px; padding: 10px; background: rgba(255, 255, 255, 0.05); border-radius: 6px;">
                            <h4 style="margin: 0 0 10px 0; color: #888;">单关节控制 (备用)</h4>
                            <div class="joint-control">
                                <div class="joint-name">腰部旋转:</div>
                                <div class="joint-keys"><span class="key" data-key="1">1</span> <span class="direction-plus">+</span> <span class="key" data-key="q">Q</span> <span class="direction-minus">-</span></div>
                            </div>
                            <div class="joint-control">
                                <div class="joint-name">大臂控制:</div>
                                <div class="joint-keys"><span class="key" data-key="2">2</span> <span class="direction-plus">+</span> <span class="key" data-key="w">W</span> <span class="direction-minus">-</span></div>
                            </div>
                            <div class="joint-control">
                                <div class="joint-name">小臂控制:</div>
                                <div class="joint-keys"><span class="key" data-key="3">3</span> <span class="direction-plus">+</span> <span class="key" data-key="e">E</span> <span class="direction-minus">-</span></div>
                            </div>
                            <div class="joint-control">
                                <div class="joint-name">腕部控制:</div>
                                <div class="joint-keys"><span class="key" data-key="4">4</span> <span class="direction-plus">+</span> <span class="key" data-key="r">R</span> <span class="direction-minus">-</span></div>
                            </div>
                            <div class="joint-control">
                                <div class="joint-name">腕部旋转:</div>
                                <div class="joint-keys"><span class="key" data-key="5">5</span> <span class="direction-plus">+</span> <span class="key" data-key="t">T</span> <span class="direction-minus">-</span></div>
                            </div>
                            <div class="joint-control">
                                <div class="joint-name">爪子控制:</div>
                                <div class="joint-keys"><span class="key" data-key="6">6</span> <span class="direction-plus">+</span> <span class="key" data-key="y">Y</span> <span class="direction-minus">-</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <br>
                <!-- Servo Status Section (initially hidden) -->
                <div class="collapsible" id="servoStatusContainer" style="display: none;">
                    <div class="collapsible-header">
                        <span>Servo Status</span>
                        <span class="collapsible-icon">▼</span>
                    </div>
                    <div class="collapsible-content">
                        <!-- Removed inline style, styles are now in <head> -->
                        <div class="servo-status-grid">
                            <div class="servo-status-row">
                                <div class="servo-name">Rotation:</div>
                                <div class="servo-status-spacer"></div>
                                <div id="servo-1-status" class="servo-status">idle</div>
                                <div id="servo-1-error" class="servo-error"></div>
                            </div>
                            <div class="servo-status-row">
                                <div class="servo-name">Pitch:</div>
                                <div class="servo-status-spacer"></div>
                                <div id="servo-2-status" class="servo-status">idle</div>
                                <div id="servo-2-error" class="servo-error"></div>
                            </div>
                            <div class="servo-status-row">
                                <div class="servo-name">Elbow:</div>
                                <div class="servo-status-spacer"></div>
                                <div id="servo-3-status" class="servo-status">idle</div>
                                <div id="servo-3-error" class="servo-error"></div>
                            </div>
                            <div class="servo-status-row">
                                <div class="servo-name">Wrist Pitch:</div>
                                <div class="servo-status-spacer"></div>
                                <div id="servo-4-status" class="servo-status">idle</div>
                                <div id="servo-4-error" class="servo-error"></div>
                            </div>
                            <div class="servo-status-row">
                                <div class="servo-name">Wrist Roll:</div>
                                <div class="servo-status-spacer"></div>
                                <div id="servo-5-status" class="servo-status">idle</div>
                                <div id="servo-5-error" class="servo-error"></div>
                            </div>
                            <div class="servo-status-row">
                                <div class="servo-name">Jaw:</div>
                                <div class="servo-status-spacer"></div>
                                <div id="servo-6-status" class="servo-status">idle</div>
                                <div id="servo-6-error" class="servo-error"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="help-wrapper">
                    <button id="connectRealRobot" class="connect-button">连接真实机械臂</button>
                    <div class="help-icon" id="connectHelpIcon">?
                        <div class="tooltip">
                            <strong>连接说明:</strong>
                            <ul>
                                <li>选择正确的端口</li>
                                <li>确保真实机械臂和仿真保持一直，以防出现事故</li>
                            </ul>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <div class="toggle-panel" id="togglePanel">Show Controls</div>

        <script type="module" src="./index.js"></script>
        <script async defer src="https://buttons.github.io/buttons.js"></script>
    </body>
</html>