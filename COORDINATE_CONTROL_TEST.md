# 机械臂6轴联动坐标控制测试指南

## 功能概述

本项目已成功修改为支持机械臂6轴联动坐标控制系统。现在可以通过键盘直接控制机械臂末端执行器在3D空间中的位置，而不是单独控制每个关节。

## 新的键盘控制映射

### 坐标控制 (主要控制方式)
- **U键**: X轴正方向移动
- **I键**: X轴负方向移动  
- **J键**: Y轴正方向移动
- **K键**: Y轴负方向移动
- **N键**: Z轴正方向移动
- **M键**: Z轴负方向移动

### 传统关节控制 (备用)
- **1/Q**: 腰部旋转 (关节1)
- **2/W**: 大臂控制 (关节2)
- **3/E**: 小臂控制 (关节3)
- **4/R**: 腕部控制 (关节4)
- **5/T**: 腕部旋转 (关节5)
- **6/Y**: 爪子控制 (关节6)

## 测试步骤

### 1. 启动应用
```bash
npx vite
```
然后在浏览器中打开 http://localhost:5173/

### 2. 观察初始状态
- 查看控制面板中的"末端执行器位置"显示
- 应该能看到当前的X、Y、Z坐标值

### 3. 测试坐标控制
1. **X轴测试**: 按住U键，观察机械臂是否向X正方向移动，末端执行器X坐标是否增加
2. **Y轴测试**: 按住J键，观察机械臂是否向Y正方向移动，末端执行器Y坐标是否增加  
3. **Z轴测试**: 按住N键，观察机械臂是否向Z正方向移动，末端执行器Z坐标是否增加

### 4. 测试逆向运动学
- 尝试组合按键，如同时按U和J，观察机械臂是否能同时在X和Y方向移动
- 观察多个关节是否协调运动以达到目标位置

### 5. 检查限位保护
- 尝试移动到极限位置，系统应该显示限位警告并阻止超出范围的运动

## 技术实现

### 正向运动学
- 基于URDF文件中的关节配置计算末端执行器位置
- 使用变换矩阵累积计算各关节的贡献

### 逆向运动学  
- 使用数值迭代方法(雅可比法)求解关节角度
- 根据目标坐标变化计算所需的关节运动

### 6轴联动
- 所有6个关节协调运动以实现末端执行器的精确定位
- 自动处理关节限位和奇异点问题

## 预期结果

1. **坐标显示**: 末端执行器位置实时更新显示
2. **平滑运动**: 机械臂运动平滑，无突跳
3. **精确控制**: 按键对应的坐标轴移动准确
4. **限位保护**: 超出工作空间时有警告提示
5. **多轴联动**: 可以同时控制多个坐标轴

## 故障排除

### 如果坐标不更新
- 检查浏览器控制台是否有JavaScript错误
- 确认机械臂模型已正确加载

### 如果运动不平滑
- 调整速度控制滑块
- 检查逆运动学求解是否收敛

### 如果按键无响应
- 确认页面已获得焦点
- 检查键盘事件监听是否正常工作

## 开发者信息

- 正向运动学函数: `forwardKinematics()`
- 逆向运动学函数: `inverseKinematics()`
- 坐标更新函数: `updateEndEffectorDisplay()`
- 键盘控制处理: `updateJoints()`中的坐标控制逻辑
