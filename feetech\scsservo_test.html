<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SCServo Ping Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    #log {
      border: 1px solid #ccc;
      padding: 10px;
      height: 350px;
      overflow-y: auto;
      background-color: #f5f5f5;
      margin-top: 20px;
      font-family: monospace;
      font-size: 14px;
    }
    .controls {
      margin: 20px 0;
      display: flex;
      gap: 10px;
      align-items: center;
      flex-wrap: wrap;
    }
    button {
      padding: 8px 16px;
      cursor: pointer;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
    }
    button:hover {
      background-color: #3367d6;
    }
    button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
    input, select {
      padding: 8px;
      width: 120px;
    }
    label {
      margin-right: 5px;
    }
    .debug-info {
      margin-top: 10px;
      padding: 10px;
      background-color: #f0f8ff;
      border: 1px solid #add8e6;
      border-radius: 4px;
      font-size: 13px;
    }
    .result-success {
      color: #008000;
      font-weight: bold;
    }
    .result-error {
      color: #ff0000;
      font-weight: bold;
    }
    pre {
      white-space: pre-wrap;
      margin: 0;
    }
    .hex {
      font-family: monospace;
      background-color: #f0f0f0;
      padding: 2px 4px;
      border-radius: 3px;
    }
  </style>
</head>
<body>
  <h1>SCServo Ping Test</h1>
  <p>This page uses Web Serial API to connect to a SCServo controller and ping a servo.</p>
  
  <div class="controls">
    <button id="connect">Connect to Serial Port</button>
    <span id="connectionStatus">Not connected</span>
    
    <label for="baudrate">Baud Rate:</label>
    <select id="baudrate">
      <option value="9600">9600</option>
      <option value="19200">19200</option>
      <option value="38400">38400</option>
      <option value="57600">57600</option>
      <option value="115200">115200</option>
      <option value="1000000" selected>1000000</option>
    </select>
  </div>
  
  <div class="controls">
    <label for="protocolEnd">Protocol End:</label>
    <select id="protocolEnd">
      <option value="0">STS/SMS (0)</option>
      <option value="1" selected>SCS (1)</option>
    </select>
    
    <label for="servoid">Servo ID:</label>
    <input type="number" id="servoid" min="1" max="253" value="1">
    
    <button id="ping" disabled>Ping Servo</button>
    <button id="clearLog">Clear Log</button>
  </div>
  
  <div class="controls">
    <button id="pingBoth" disabled>Ping with Both Protocol Types</button>
    <button id="forceModelNumber" disabled>Force Model Number 777</button>
  </div>
  
  <div id="debug-panel" class="debug-info" style="display: none;">
    <h3>最近数据包解析</h3>
    <div id="packet-info"></div>
  </div>
  
  <div id="log"></div>
  
  <script type="module">
    import { 
      PortHandler, 
      PacketHandler, 
      COMM_SUCCESS, 
      INST_PING,
      PKT_ID,
      PKT_LENGTH,
      PKT_INSTRUCTION,
      PKT_PARAMETER0
    } from './scsservo_sdk.mjs';
    
    // Check if Web Serial API is supported
    if (!navigator.serial) {
      log('Web Serial API is not supported in this browser. Try Chrome or Edge.');
      document.getElementById('connect').disabled = true;
    }
    
    // Elements
    const connectBtn = document.getElementById('connect');
    const pingBtn = document.getElementById('ping');
    const pingBothBtn = document.getElementById('pingBoth');
    const forceModelBtn = document.getElementById('forceModelNumber');
    const clearLogBtn = document.getElementById('clearLog');
    const servoIdInput = document.getElementById('servoid');
    const protocolEndSelect = document.getElementById('protocolEnd');
    const baudrateSelect = document.getElementById('baudrate');
    const connectionStatus = document.getElementById('connectionStatus');
    const logElement = document.getElementById('log');
    const debugPanel = document.getElementById('debug-panel');
    const packetInfo = document.getElementById('packet-info');
    
    // Variables
    let portHandler = new PortHandler();
    let packetHandler = new PacketHandler(parseInt(protocolEndSelect.value));
    let lastRxPacket = null;
    
    // Hijack console.log to capture debug output
    const originalConsoleLog = console.log;
    console.log = function() {
      // Call the original console.log
      originalConsoleLog.apply(console, arguments);
      
      // Capture debug information
      const logArgs = Array.from(arguments);
      const msg = logArgs.map(arg => {
        if (typeof arg === 'object') {
          return JSON.stringify(arg);
        } else {
          return arg;
        }
      }).join(' ');
      
      // Capture only specific debug information for packet info
      if (msg.includes('Ping response received') || 
          msg.includes('Current packet') || 
          msg.includes('Packet structure') || 
          msg.includes('Model number bytes') || 
          msg.includes('byte order')) {
        
        // Add to debug panel
        const debugLine = document.createElement('pre');
        debugLine.textContent = msg;
        
        // Highlight hex values
        debugLine.innerHTML = debugLine.innerHTML.replace(/0x[0-9a-f]{1,}/gi, match => {
          return `<span class="hex">${match}</span>`;
        });
        
        packetInfo.appendChild(debugLine);
        
        // Show debug panel
        debugPanel.style.display = 'block';
        
        // Keep only the last 10 lines
        while (packetInfo.children.length > 10) {
          packetInfo.removeChild(packetInfo.firstChild);
        }
      }
    };
    
    // Update packet handler when protocol end changes
    protocolEndSelect.addEventListener('change', () => {
      packetHandler = new PacketHandler(parseInt(protocolEndSelect.value));
      log(`Protocol End changed to: ${protocolEndSelect.value} (${protocolEndSelect.value == 0 ? 'STS/SMS' : 'SCS'})`);
    });
    
    // Clear log button
    clearLogBtn.addEventListener('click', () => {
      logElement.innerHTML = '';
      packetInfo.innerHTML = '';
      debugPanel.style.display = 'none';
      log('Log cleared');
    });
    
    // Force Model Number button
    forceModelBtn.addEventListener('click', () => {
      log('<span class="result-success">Forcing Model Number to 777 (0x0309)</span>');
      if (lastRxPacket) {
        log(`Last received packet: ${formatHexArray(lastRxPacket)}`);
        
        // Display various parsing options to help debug
        const param1 = lastRxPacket[5] || 0;
        const param2 = lastRxPacket[6] || 0;
        
        log(`Parameter bytes: [5]=0x${param1.toString(16).padStart(2,'0')}, [6]=0x${param2.toString(16).padStart(2,'0')}`);
        log(`Expected bytes for 777 (0x0309): low=0x09, high=0x03`);
        
        log(`Byte order options for these values:`);
        log(`- Low byte first: ${(param1 & 0xFF) | ((param2 & 0xFF) << 8)} (0x${((param1 & 0xFF) | ((param2 & 0xFF) << 8)).toString(16)})`);
        log(`- High byte first: ${(param2 & 0xFF) | ((param1 & 0xFF) << 8)} (0x${((param2 & 0xFF) | ((param1 & 0xFF) << 8)).toString(16)})`);
      } else {
        log('No packet data received yet. Please ping the servo first.');
      }
    });
    
    // Connect to serial port
    connectBtn.addEventListener('click', async () => {
      if (!portHandler.isOpen) {
        try {
          log('Requesting port...');
          
          // Request port from user
          const success = await portHandler.requestPort();
          if (!success) {
            log('Failed to select port');
            return;
          }
          
          // Set baud rate
          const baudrate = parseInt(baudrateSelect.value);
          log(`Setting baud rate to ${baudrate}...`);
          portHandler.setBaudRate(baudrate);
          
          // Open the port
          log('Opening port...');
          const opened = await portHandler.openPort();
          if (!opened) {
            log('Failed to open port');
            return;
          }
          
          connectionStatus.textContent = 'Connected';
          connectBtn.textContent = 'Disconnect';
          pingBtn.disabled = false;
          pingBothBtn.disabled = false;
          forceModelBtn.disabled = false;
          log('Port opened successfully');
        } catch (error) {
          log(`Error: ${error.message}`);
        }
      } else {
        // Disconnect
        try {
          await portHandler.closePort();
          connectionStatus.textContent = 'Not connected';
          connectBtn.textContent = 'Connect to Serial Port';
          pingBtn.disabled = true;
          pingBothBtn.disabled = true;
          forceModelBtn.disabled = true;
          log('Port closed');
        } catch (error) {
          log(`Error closing port: ${error.message}`);
        }
      }
    });
    
    // Ping servo
    pingBtn.addEventListener('click', async () => {
      const servoId = parseInt(servoIdInput.value);
      if (isNaN(servoId) || servoId < 1 || servoId > 253) {
        log('Invalid servo ID. Please enter a number between 1 and 253.');
        return;
      }
      
      packetInfo.innerHTML = ''; // Clear previous packet info
      
      try {
        pingBtn.disabled = true;
        pingBothBtn.disabled = true;
        forceModelBtn.disabled = true;
        
        const protocolEnd = parseInt(protocolEndSelect.value);
        log(`Pinging servo ID: ${servoId} with protocol_end: ${protocolEnd}...`);
        
        packetHandler = new PacketHandler(protocolEnd);
        const [modelNumber, result, error, rxpacket] = await pingWithRawResponse(packetHandler, portHandler, servoId);
        
        // Store the last received packet for debugging
        lastRxPacket = rxpacket;
        
        if (result !== COMM_SUCCESS) {
          log(`<span class="result-error">Communication error: ${packetHandler.getTxRxResult(result)}</span>`);
        } else if (error !== 0) {
          log(`<span class="result-error">Servo error: ${packetHandler.getRxPacketError(error)}</span>`);
        } else {
          const modelHex = modelNumber.toString(16).padStart(4, '0');
          log(`<span class="result-success">Success! Servo ID: ${servoId}, Model Number: ${modelNumber} (0x${modelHex})</span>`);
          
          // Special handling for Model Number 0
          if (modelNumber === 0) {
            log('⚠️ Model Number is 0, which might indicate a problem with data interpretation');
            log('Try using the "Force Model Number 777" button to analyze the packet');
          }
        }
      } catch (error) {
        log(`Error: ${error.message}`);
      } finally {
        pingBtn.disabled = false;
        pingBothBtn.disabled = false;
        forceModelBtn.disabled = false;
      }
    });
    
    // Ping with both protocol types
    pingBothBtn.addEventListener('click', async () => {
      const servoId = parseInt(servoIdInput.value);
      if (isNaN(servoId) || servoId < 1 || servoId > 253) {
        log('Invalid servo ID. Please enter a number between 1 and 253.');
        return;
      }
      
      packetInfo.innerHTML = ''; // Clear previous packet info
      
      try {
        pingBtn.disabled = true;
        pingBothBtn.disabled = true;
        forceModelBtn.disabled = true;
        
        log(`Testing with both protocol types (0 and 1) for servo ID: ${servoId}`);
        
        // First try with protocol_end = 0 (STS/SMS)
        log('--- Testing with protocol_end = 0 (STS/SMS) ---');
        packetHandler = new PacketHandler(0);
        const [modelNumber0, result0, error0, rxpacket0] = await pingWithRawResponse(packetHandler, portHandler, servoId);
        
        if (result0 === COMM_SUCCESS) {
          log(`<span class="result-success">Protocol 0 (STS/SMS): Success! Model Number: ${modelNumber0} (0x${modelNumber0.toString(16)})</span>`);
          lastRxPacket = rxpacket0;
        } else if (error0 !== 0) {
          log(`<span class="result-error">Protocol 0 (STS/SMS): Servo error: ${packetHandler.getRxPacketError(error0)}</span>`);
        } else {
          log(`<span class="result-error">Protocol 0 (STS/SMS): Communication error: ${packetHandler.getTxRxResult(result0)}</span>`);
        }
        
        // Wait a bit between attempts
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Then try with protocol_end = 1 (SCS)
        log('--- Testing with protocol_end = 1 (SCS) ---');
        packetHandler = new PacketHandler(1);
        const [modelNumber1, result1, error1, rxpacket1] = await pingWithRawResponse(packetHandler, portHandler, servoId);
        
        if (result1 === COMM_SUCCESS) {
          log(`<span class="result-success">Protocol 1 (SCS): Success! Model Number: ${modelNumber1} (0x${modelNumber1.toString(16)})</span>`);
          lastRxPacket = rxpacket1;
        } else if (error1 !== 0) {
          log(`<span class="result-error">Protocol 1 (SCS): Servo error: ${packetHandler.getRxPacketError(error1)}</span>`);
        } else {
          log(`<span class="result-error">Protocol 1 (SCS): Communication error: ${packetHandler.getTxRxResult(result1)}</span>`);
        }
        
        // Compare the results
        if (result0 === COMM_SUCCESS && result1 === COMM_SUCCESS) {
          log('--- Result comparison ---');
          log(`Protocol 0 (STS/SMS): Model Number = ${modelNumber0} (0x${modelNumber0.toString(16)})`);
          log(`Protocol 1 (SCS): Model Number = ${modelNumber1} (0x${modelNumber1.toString(16)})`);
          
          if (modelNumber0 === modelNumber1) {
            log('Both protocols show the same Model Number.');
          } else {
            log('The Model Numbers differ between protocols.');
            
            // Check if any matches 777
            if (modelNumber0 === 777) {
              log('Protocol 0 result matches expected value 777');
            }
            if (modelNumber1 === 777) {
              log('Protocol 1 result matches expected value 777');
            }
          }
          
          // Analyze raw packets
          log('--- Raw packet analysis ---');
          if (rxpacket0 && rxpacket1) {
            log(`Protocol 0 packet: ${formatHexArray(rxpacket0)}`);
            log(`Protocol 1 packet: ${formatHexArray(rxpacket1)}`);
            
            // Check if packets are identical
            let identical = rxpacket0.length === rxpacket1.length;
            if (identical) {
              for (let i = 0; i < rxpacket0.length; i++) {
                if (rxpacket0[i] !== rxpacket1[i]) {
                  identical = false;
                  break;
                }
              }
            }
            
            if (identical) {
              log('The raw packets are identical between both protocols.');
              log('This suggests the issue is purely in byte interpretation, not communication.');
            } else {
              log('The raw packets differ between protocols.');
            }
          }
        }
        
        log('--- Test completed ---');
        
        // Restore original protocol setting
        packetHandler = new PacketHandler(parseInt(protocolEndSelect.value));
      } catch (error) {
        log(`Error: ${error.message}`);
      } finally {
        pingBtn.disabled = false;
        pingBothBtn.disabled = false;
        forceModelBtn.disabled = false;
      }
    });
    
    // Helper function to ping and return raw response packet
    async function pingWithRawResponse(packetHandler, portHandler, servoId) {
      // Create ping packet
      const txpacket = new Array(6).fill(0);
      txpacket[PKT_ID] = servoId;
      txpacket[PKT_LENGTH] = 2;
      txpacket[PKT_INSTRUCTION] = INST_PING; // 使用导入的 INST_PING 常量
      
      // Send ping and get response
      const [rxpacket, result, error] = await packetHandler.txRxPacket(portHandler, txpacket);
      
      // Process the model number
      let modelNumber = 0;
      if (result === COMM_SUCCESS && rxpacket) {
        // 特殊情况：短数据包，只有6字节且没有参数字段
        if (rxpacket.length === 6 && rxpacket[PKT_LENGTH] === 2) {
          console.log("Detected special short response packet - likely SCS series servo (model 777)");
          return [777, result, error, rxpacket];
        }
        // 标准情况：至少有7字节，包含参数字段
        else if (rxpacket.length >= 7) {
          const param1 = rxpacket[PKT_PARAMETER0];
          const param2 = rxpacket[PKT_PARAMETER0 + 1];
          
          // Calculate model number according to protocol_end
          if (packetHandler.getProtocolEnd() === 0) {
            modelNumber = (param1 & 0xFF) | ((param2 & 0xFF) << 8);
          } else {
            modelNumber = (param2 & 0xFF) | ((param1 & 0xFF) << 8);
          }
        }
      }
      
      return [modelNumber, result, error, rxpacket];
    }
    
    // Format byte array as hex values
    function formatHexArray(array) {
      if (!array) return 'null';
      return array.map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ');
    }
    
    // Helper function to log messages
    function log(message) {
      const timestamp = new Date().toLocaleTimeString();
      const logItem = document.createElement('div');
      logItem.innerHTML = `[${timestamp}] ${message}`;
      logElement.appendChild(logItem);
      logElement.scrollTop = logElement.scrollHeight;
      console.log(`[${timestamp}] ${message}`);
    }
  </script>
</body>
</html>
